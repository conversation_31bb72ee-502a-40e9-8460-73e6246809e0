; PHP-FPM 性能优化配置
; 解决20+秒加载时间问题

; 进程管理器类型
pm = dynamic

; 大幅增加进程数量以处理并发请求
pm.max_children = 50          ; 从5增加到50 - 最大子进程数
pm.start_servers = 10         ; 从2增加到10 - 启动时的进程数
pm.min_spare_servers = 5      ; 从1增加到5 - 最少空闲进程
pm.max_spare_servers = 15     ; 从3增加到15 - 最多空闲进程

; 进程生命周期管理
pm.max_requests = 1000        ; 每个进程处理1000个请求后重启
pm.process_idle_timeout = 30s ; 空闲进程30秒后终止

; 请求超时设置
request_terminate_timeout = 60s ; 请求60秒超时
request_slowlog_timeout = 10s   ; 10秒以上的请求记录到慢日志

; 慢日志配置
slowlog = /var/log/php-fpm-slow.log

; 状态页面
pm.status_path = /fpm-status

; 访问日志优化
access.log = /var/log/php-fpm-access.log
access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"

; 安全设置
security.limit_extensions = .php .phar

; 环境变量
clear_env = no

; 捕获输出
catch_workers_output = yes
