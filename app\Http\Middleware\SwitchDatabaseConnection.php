<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class SwitchDatabaseConnection
{
    public function handle($request, Closure $next)
    {
        // Проверяем, выполняется ли запрос в Filament
        if ($request->is('admin/*')) {
            return $next($request);
        }

        // 获取服务器连接，但避免不必要的重连
        $connection = gameservers();

        // 只有在连接不存在或无效时才重新连接
        try {
            DB::connection($connection)->getPdo();
        } catch (\Exception $e) {
            // 只有在连接失败时才重新连接
            DB::purge($connection);
            DB::reconnect($connection);
        }

        return $next($request);
    }

}
