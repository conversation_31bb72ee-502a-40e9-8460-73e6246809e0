@extends('template::layouts.public')

@section('title', '前端渲染速度测试')

@section('meta_description', '前端渲染速度测试页面 - 纯静态内容，无后端逻辑')

@section('content')
<div class="container">
    <div class="speed-test-page">
        <!-- 页面加载时间显示 -->
        <div class="load-time-display">
            <h1>🚀 前端渲染速度测试</h1>
            <div class="time-info">
                <p>页面开始加载时间: <span id="start-time"></span></p>
                <p>页面加载完成时间: <span id="end-time"></span></p>
                <p>总加载时间: <span id="total-time"></span></p>
            </div>
        </div>

        <!-- 测试内容区域 -->
        <div class="test-content">
            <div class="test-section">
                <h2>📊 测试信息</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <h3>页面类型</h3>
                        <p>纯静态页面</p>
                    </div>
                    <div class="info-card">
                        <h3>后端逻辑</h3>
                        <p>无任何数据库查询</p>
                    </div>
                    <div class="info-card">
                        <h3>测试目的</h3>
                        <p>检测前端渲染性能</p>
                    </div>
                    <div class="info-card">
                        <h3>当前时间</h3>
                        <p id="current-time"></p>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h2>🎨 样式测试</h2>
                <div class="style-test">
                    <div class="color-boxes">
                        <div class="color-box red">红色</div>
                        <div class="color-box green">绿色</div>
                        <div class="color-box blue">蓝色</div>
                        <div class="color-box yellow">黄色</div>
                    </div>
                    
                    <div class="animation-test">
                        <div class="rotating-box">旋转动画</div>
                        <div class="pulsing-box">脉冲动画</div>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h2>📝 内容测试</h2>
                <div class="content-test">
                    <p>这是一个用于测试前端渲染速度的页面。页面包含以下元素：</p>
                    <ul>
                        <li>静态HTML内容</li>
                        <li>CSS样式和动画</li>
                        <li>JavaScript交互功能</li>
                        <li>响应式布局</li>
                    </ul>
                    
                    <div class="lorem-text">
                        <h3>示例文本内容</h3>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h2>⚡ 性能指标</h2>
                <div class="performance-metrics">
                    <div class="metric-card">
                        <h4>DOM 加载时间</h4>
                        <span id="dom-time">计算中...</span>
                    </div>
                    <div class="metric-card">
                        <h4>资源加载时间</h4>
                        <span id="resource-time">计算中...</span>
                    </div>
                    <div class="metric-card">
                        <h4>页面完全加载时间</h4>
                        <span id="complete-time">计算中...</span>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h2>🔄 交互测试</h2>
                <div class="interaction-test">
                    <button id="test-button" class="test-btn">点击测试响应速度</button>
                    <div id="click-result" class="click-result"></div>
                    
                    <div class="counter-test">
                        <p>点击计数器: <span id="click-counter">0</span></p>
                        <button id="increment-btn" class="increment-btn">+1</button>
                        <button id="reset-btn" class="reset-btn">重置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.speed-test-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.load-time-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}

.load-time-display h1 {
    margin: 0 0 20px 0;
    font-size: 2.5em;
}

.time-info p {
    margin: 10px 0;
    font-size: 1.1em;
}

.test-section {
    background: white;
    padding: 25px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.test-section h2 {
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    text-align: center;
}

.info-card h3 {
    color: #495057;
    margin-bottom: 10px;
}

.color-boxes {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.color-box {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    border-radius: 8px;
}

.color-box.red { background: #e74c3c; }
.color-box.green { background: #2ecc71; }
.color-box.blue { background: #3498db; }
.color-box.yellow { background: #f1c40f; color: #333; }

.animation-test {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.rotating-box {
    width: 100px;
    height: 100px;
    background: #9b59b6;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    animation: rotate 2s linear infinite;
}

.pulsing-box {
    width: 100px;
    height: 100px;
    background: #e67e22;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.metric-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    text-align: center;
}

.metric-card h4 {
    margin-bottom: 10px;
    color: #495057;
}

.metric-card span {
    font-size: 1.2em;
    font-weight: bold;
    color: #007bff;
}

.interaction-test {
    text-align: center;
}

.test-btn, .increment-btn, .reset-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    margin: 10px;
    transition: background 0.3s;
}

.test-btn:hover, .increment-btn:hover {
    background: #0056b3;
}

.reset-btn {
    background: #dc3545;
}

.reset-btn:hover {
    background: #c82333;
}

.click-result {
    margin: 20px 0;
    padding: 15px;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    display: none;
}

.counter-test {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
}

.counter-test p {
    font-size: 1.2em;
    margin-bottom: 15px;
}

#click-counter {
    font-weight: bold;
    color: #007bff;
    font-size: 1.5em;
}

@media (max-width: 768px) {
    .speed-test-page {
        padding: 10px;
    }
    
    .load-time-display h1 {
        font-size: 2em;
    }
    
    .color-boxes, .animation-test {
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 记录页面加载时间
    const startTime = performance.timing.navigationStart;
    const domTime = performance.timing.domContentLoaded;
    const endTime = performance.timing.loadEventEnd;
    
    // 显示加载时间
    document.getElementById('start-time').textContent = new Date(startTime).toLocaleTimeString();
    document.getElementById('end-time').textContent = new Date().toLocaleTimeString();
    document.getElementById('total-time').textContent = (Date.now() - startTime) + 'ms';
    
    // 显示当前时间
    function updateCurrentTime() {
        document.getElementById('current-time').textContent = new Date().toLocaleString();
    }
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // 性能指标
    window.addEventListener('load', function() {
        setTimeout(function() {
            const navigation = performance.getEntriesByType('navigation')[0];
            
            document.getElementById('dom-time').textContent = 
                Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart) + 'ms';
            
            document.getElementById('resource-time').textContent = 
                Math.round(navigation.loadEventStart - navigation.domContentLoadedEventEnd) + 'ms';
            
            document.getElementById('complete-time').textContent = 
                Math.round(navigation.loadEventEnd - navigation.navigationStart) + 'ms';
        }, 100);
    });
    
    // 交互测试
    let clickCount = 0;
    
    document.getElementById('test-button').addEventListener('click', function() {
        const clickTime = performance.now();
        const result = document.getElementById('click-result');
        result.innerHTML = `✅ 按钮响应时间: ${clickTime.toFixed(2)}ms<br>点击时间: ${new Date().toLocaleTimeString()}`;
        result.style.display = 'block';
    });
    
    document.getElementById('increment-btn').addEventListener('click', function() {
        clickCount++;
        document.getElementById('click-counter').textContent = clickCount;
    });
    
    document.getElementById('reset-btn').addEventListener('click', function() {
        clickCount = 0;
        document.getElementById('click-counter').textContent = clickCount;
    });
});
</script>
@endsection
