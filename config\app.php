<?php

use Illuminate\Support\Facades\Facade;
use Illuminate\Support\ServiceProvider;

return [
    'name' => 'XXTT2',
    'template' => 'default', // Тема по умолчанию
    'l2server_type' => 'pts', // Тип сервера java или pts?
    'l2server_version' => 'vaganth', // Какая у вас сборка сервера? доступные l2jscripts, lucera2, vaganth, l2eternity, depmax, smeli, acis, fandc (l2 tales)
    'password_hash' => 'hAuth', // Какую шифрацию пароля использует сервер? sha1, acis, whirlpool, md5, bcrypt, по умолчанию whirlpool, (hAuth для PTS)
    'email_verification' => false, // Включить активацию по почте?

    // Если эта опция Включена, то при регистрации будет создаваться Мастер и Игровой аккаунт!
    // Если опция включена можно будет заходить на сайт только с игровым аккаунтом! БЕЗ Мастер аккаунта
    // Не Включать если не знаете для чего это!
    'game_account_system' => false,

    // Синхронизировать игровые аккаунты с мастер аккаунтом
    // Если вы использовали Mmoweb, Stressweb, Ghtweb либо любые другие движки это поможет вам привязать игровой логин к вашему созданному мастеру аккаунту
    'sync_game_accounts' => false,
    'sync_ban_time' => 10, // Время на сколько баним игрока если ввел не правильно данные (по умолчанию 10минут)

    'sync_mmoweb_enabled' => false, // Синхронизация аккаунтов с ммовеб, не включать если не знаете для чего это! работает совместно с активацией по почте

    // Настройка серверов
    'java_servers' => [
        'server1' => [],
    ],
    'loginserver' => [],

    // Настройка серверов для PTS, Vaganth
    'pts_servers' => [
        'lin2world' => [
            'server_name' => '经典196',
            'host'      => '*************',
            'port'      => '1433',
            'database'  => 'lin2world',
            'username'  => 'sa',
            'password'  => 'fdjhjdfkJFDJ5165JFDJjdfj!@#',
        ],
    ],
    'lin2db' => [
        'server_name' => 'PTS',
        'host'      => '*************',
        'port'      => '1433',
        'database'  => 'lin2db',
        'username'  => 'sa',
        'password'  => 'fdjhjdfkJFDJ5165JFDJjdfj!@#',
    ],

    'lin2billing' => [],

    // 移除闭包以支持配置缓存 - 改用Helper类方法
    // 'getServers' => function () { ... } // 已移除，使用 Helper::getServers() 替代

    'enable_online' => true, // Показывать онлайн статус серверов?
    'server_status_cached_time' => 0, // Укажите время кеширование онлайна? каждые 10мин

    'referral_system_enabled' => true, // Вкл. Выкл Реферальная система
    'referral_bonus_percent' => 10, // Сколько получит бонус в колах? Если реферал пополнит баланс? по умолчанию получит 10%

    'profile_cached_time' => 1, // Закешировать данные в /dashboard появляется список игровых аккаунтов кешируем данные (по умолчанию 1800 - 30минут)

    // Статистика
    'statistics' => [
        'stats_cached' => 0, // Закешировать данные всей статистики, рекомендуется для быстродействия

        // Показывает количество в /stats
        'accounts_count_enabled' => true, // показывать Аккаунты
        'characters_count_enabled' => true, // показывать сколько Персонажей
        'nobles_count_enabled' => false, // показывать сколько Нублесов
        'heroes_count_enabled' => false, // показывать сколько Героев
        'gm_count_enabled' => false, // показывать сколько ГМ
        'clans_count_enabled' => true, // показывать Кланы
        'alliance_count_enabled' => true, // показывать Альянсы
        'gender_stats_enabled' => true, // Для sexman и sexwoman
        'beleth_stats_enabled' => true, // Отключить белефа в статистике Эпик боссы да нет
        'races_stats_enabled' => true, // Отключить Расы в статистике

        // лимит вывода в статистике:?
        'top_pvp_limit' => 10, // Топ пвп по умолчанию выводит 20
        'top_pk_limit' => 10, // Топ пк по умолчанию выводит 20
        'top_players_limit' => 10, // Топ игроки по умолчанию выводит 20
        'clan_limit' => 10, // Альянсы по умолчанию выводит 20
        'alliance_limit' => 10, // Альянсы по умолчанию выводит 20

        // Статистика на главной странице Вкл/Выкл
        'enable_top_pvp' => true,
        'top_pvp_count' => 9,

        'enable_top_pk' => false,

        'enable_clans' => true,
        'top_clans_count' => 9,

        'enable_castles' => false,
    ],

    // Настройка платежных систем
    'pay_system' => [
        'item_id' => 4037, // id итема который выдается игрокам, при донате без авторизации
        'item_price' => 45, // цена итема
        'currency' => 'RUB', // валюта
        'min_price' => 10, // минимальная цена к покупке
        'symbol' => '₽',

        'coin_price' => [
            'USD' => 1.00,  // Цена за 1 кол в долларах
            'EUR' => 0.90,  // Цена за 1 кол в евро
            'RUB' => 75.00, // Цена за 1 кол в рублях
        ],

        // Коэффициенты для валют Это курс в разных валютах
        'currency_rate' => [
            'USD' => 1,    // Коэффициент для доллара
            'EUR' => 1.1, // Коэффициент для евро
            'RUB' => 130,   // Коэффициент для рубля
        ],

        // настройка фрикассы
        'freekassa_enable' => true,
        'freekassa_currency' => 'RUB',
        'freekassa_project_id' => 42389,
        'freekassa_secret_key' => 'fi77webal0G7U*j',
        'freekassa_secret_key_2' => '/QFay^gg9=e+JZx',
        'freekassa_desc' => 'Вы покупаете ',

        // настройка morune бывший enot
        'morune_enable' => true,
        'morune_currency' => 'RUB',
        'morune_shop_id' => 'c99cc6d6-110d-45c6-b09d-956b80df8140', // ID магазина в UUID
        'morune_secret_key' => '4afafc3540bbd30d22f9e6c7ba91741df2cfc7ad', // Секретный ключ
        'morune_custom_key' => '2a7ffe9334aacd4641ff91ceadcbde8b1b5b8a2b', // Дополнительный ключ
        'morune_success_url' => 'https://linova.fun/profile',
        'morune_fail_url' => 'https://linova.fun/fail',
        'morune_min_count_donate' => 200,

        // настройка енота
        'enot_enable' => false,
        'enot_currency' => 'RUB',
        'enot_shop_id' => 'ea11debc-0e8f-4bb8-b51c-e174f6eafe0d',
        'enot_api_key' => '0d35f3f5387d45c101b430ffda83468068aa719e',
        'enot_secret_key' => 'd9a0de68fb219545e636f16a10a4748cfa42de23',

        // настройка primepayments
        'primepayments_enable' => false,
        'primepayments_currency' => 'RUB',
        'primepayments_project_id' => 3609,
        'primepayments_secret_key_1' => 'CmW37QfHvG',
        'primepayments_secret_key_2' => 'xS3gShTEPD',

        // настройка PayPal
        'paypal_enable' => true,
        'paypal_currency' => 'USD',
        'paypal_mode' => 'sandbox', // mode sandbox or live
        'paypal_sandbox_client_id' => 'Aa1o1CvaYRODeZ4evp0d-0rFOldt6dBSj081l1Cwp6NKOD7WwBrCLN82pHGke_zzig3ovucStqcwsPOj', // Aa1o1CvaYRODeZ4evp0d-0rFOldt6dBSj081l1Cwp6NKOD7WwBrCLN82pHGke_zzig3ovucStqcwsPOj
        'paypal_sandbox_client_secret' => 'EM-t-_Zc9J2WdlAJ5zd8O1Doekp6BD0mzNj3AP63Tx56PzwGUuTAFELepsV2FhKlhKWEI7JNtYMLUv8J', // EM-t-_Zc9J2WdlAJ5zd8O1Doekp6BD0mzNj3AP63Tx56PzwGUuTAFELepsV2FhKlhKWEI7JNtYMLUv8J
        'paypal_sandbox_app_id' => 'your key',
        // Paypal Live
        'paypal_live_client_id' => 'AcG3qcoz4I8Zyg-Q8aP8y-4Tgw2zhwRDwFCFZYXw4-Y9zdRMKtFwPSVTflkK6eKUAQvCAv_Hf5nIRLgi',
        'paypal_live_client_secret' => 'EIWtThWLCs_gEXY2LA7uWs6mDSTbCA1P8-Q7o70Yz6a2a-RY_ApHRPQ2aXjWk0Ai2UMh6cnsac_tpnbv',
        'paypal_live_app_id' => 'APP-53444811S9281964L',

        // настройка Stripe
        'stripe_enable' => true,
        'stripe_currency' => 'USD',
        'stripe_secret_key' => '',
        'stripe_public_key' => '',
        'stripe_webhook_secret' => '',

        // midtrans.com
        'midtrans_enable' => false,
        'midtrans_merchant_id' => 'G570175478',
        'midtrans_client_key' => 'SB-Mid-client-6voK7TznHUgw5MUD',
        'midtrans_server_key' => 'SB-Mid-server-fHhehxTxCVNRCIevmVFHcbxU',
        'midtrans_is_production' => false,
        'midtrans_currency' => 'IDR',

    ],

    // Бонусная система выдаем процент от стоимости колов
    // пример если купили больше 50 колов то + 6%
    // если купили больше 100 то + 9% и тд
    'bonus_system_percent_enable' => true, // Включить бонусную систему?
    'bonus_system_percent' => [
        'bonus_levels' => [
            70 => ['bonus_percent' => 6], // Бонус в процентах
            100 => ['bonus_percent' => 9], // Бонус в процентах
            150 => ['bonus_percent' => 12], // Бонус в процентах
            200 => ['bonus_percent' => 15], // Бонус в процентах
            250 => ['bonus_percent' => 20], // Бонус в процентах
            500 => ['bonus_percent' => 25], // Бонус в процентах
        ],
        'min_coins' => 70, // Минимальное количество монет для начисления бонуса
    ],

    'bonus_cache_item' => 20, // установить кеширование для итемов чтобы постоянно не парсить иконки, название итема в минутах

    'starterpacks_enabled' => true, // Включить стартепаки в личном кабинете?
    'starterpacks_repeat' => true, // Если true, то Стартерпаки могут купить только один раз!
    'starterpacks' => [
        'c_grade_pack' => [
            'title' => '新人礼包',
            'discount' => 20,
            'price' => 200,
            'items' => [
                ['item_id' => 951, 'item_count' => 5, 'enchant' => 0],     // Enchant Weapon C
                ['item_id' => 952, 'item_count' => 5, 'enchant' => 0],     // Enchant Armor C
                ['item_id' => 1464, 'item_count' => 2000, 'enchant' => 0], // Soulshot C
                ['item_id' => 2509, 'item_count' => 2000, 'enchant' => 0], // Spiritshot C
                ['item_id' => 1374, 'item_count' => 20, 'enchant' => 0],   // Greater Healing Potion (ЦМ)
                ['item_id' => 728,  'item_count' => 50, 'enchant' => 0],   // Хитрость Мудреца (ХМ)
                ['item_id' => 1539, 'item_count' => 100, 'enchant' => 0],  // Mana Potion (МП)
            ],
        ],

        'b_grade_pack' => [
            'title' => '进阶礼包',
            'discount' => 25,
            'price' => 300,
            'items' => [
                ['item_id' => 947, 'item_count' => 5, 'enchant' => 0],     // Enchant Weapon B
                ['item_id' => 948, 'item_count' => 5, 'enchant' => 0],     // Enchant Armor B
                ['item_id' => 1465, 'item_count' => 3000, 'enchant' => 0], // Soulshot B
                ['item_id' => 2510, 'item_count' => 3000, 'enchant' => 0], // Spiritshot B
                ['item_id' => 1374, 'item_count' => 30, 'enchant' => 0],   // ЦМ
                ['item_id' => 728,  'item_count' => 70, 'enchant' => 0],   // ХМ
                ['item_id' => 1539, 'item_count' => 150, 'enchant' => 0],  // МП
            ],
        ],

        'a_grade_pack' => [
            'title' => '高级礼包',
            'discount' => 30,
            'price' => 500,
            'items' => [
                ['item_id' => 949, 'item_count' => 10, 'enchant' => 0],    // Enchant Weapon A
                ['item_id' => 950, 'item_count' => 10, 'enchant' => 0],    // Enchant Armor A
                ['item_id' => 1466, 'item_count' => 5000, 'enchant' => 0], // Soulshot A
                ['item_id' => 2510, 'item_count' => 5000, 'enchant' => 0], // Spiritshot A
                ['item_id' => 1374, 'item_count' => 50, 'enchant' => 0],   // ЦМ
                ['item_id' => 728,  'item_count' => 100, 'enchant' => 0],  // ХМ
                ['item_id' => 1539, 'item_count' => 300, 'enchant' => 0],  // МП
            ],
        ],
    ],



    // команда унстук укажите координаты и локацию куда будет перещещен персонаж
    'unstuck' => [
        'unstuck_expire_time' => 60, // время до повтороного нажатия на unstuck в минутах
        'town' => 'Giran',
        'x' => '83432',
        'y' => '148552',
        'z' => '-3408',
    ],
    // сервисы услуги
    'services' => [
        'services_enabled' => false, // Вкл./Выкл сервисы

        'change_name_enabled' => true, // Вкл./Выкл. Изменить ник персонажа
        'change_name_price' => 250, // Цена услуги Pizza

        'change_color_name_enabled' => true, // Вкл./Выкл. Изменить цвет ника персонажа
        'change_color_name_price' => 300, // Цена услуги Pizza

        'change_gender_enabled' => true, // Вкл./Выкл. Изменить пол персонажа
        'change_gender' => 150, // Цена услуги Pizza
    ],
    // кастомные настройки
    'custom_config' => [
        'telegram_support' => 'https://t.me/lin2web', // Ссылка на телеграм тех поддержки
        'link_back_site' => 'https://lin2web.com/', // Ссылка на сайт
        'donate_coin_name' => '金币', // Название донат итема отображаемый на сайте
        'coin_symbol' => '金币', // тут короткое название итема к примеру CoL
    ],
    // Включить последние темы с форума xenforo?
    'FORUM_XF_ENABLED' => false,
    'XF_URL_FORUM' => 'https://forum.novagames.online/api/threads', // укажите путь до api форума пример: https://forum.fafurion.net/api/threads
    'XF_API_KEY' => 'MGXAQjjwCTObKwAPnFlYThOlH3ZxI5MZ', // апи ключ xenforo
    'XF_API_USER' => '1', // ID пользователя с необходимыми разрешениями

    'FORUM_SMF_ENABLED' => false,
    'SMF_URL' => 'https://web.l2classic.lt/forum',

    'STREAMER_ENABLED' => false, // включить показ стримеров на сайте?
    // Twitch Trovo ключи
    'TWITCH_CLIENT_ID' => '******************************',
    'TWITCH_CLIENT_SECRET' => '******************************',
    'TROVO_CLIENT_ID' => '38c3e0e2cb9508b085be4d0cfbf7455a',
    'TROVO_CLIENT_SECRET' => 'a1b5ad789410b0e34280057038191136',
    // Здесь указываем Twitch Username
    'streamerTwitch' => [
        ['twitch_username' => 'rostislav_999'],
        ['twitch_username' => 'lbeyourself'],
        ['twitch_username' => 'just_ns'],
        ['twitch_username' => 'waveafterwave2ndlife'],
        ['twitch_username' => 'bohpts'],
        ['twitch_username' => 'fisher'],
        ['twitch_username' => 'stray228'],
        ['twitch_username' => 'dyrachyo'],
        ['twitch_username' => 'burzum1349'],
        ['twitch_username' => 'OCo3HaHue'],
    ],
    // Здесь указываем Trovo Username
    'streamerTrovo' => [
        ['trovo_username' => 'Lelik12']
    ],

    // Миграция двух серверов
    'merge_servers' => [
        'src_server' => [ // база откуда копируем данные
            'server_name' => 'Cowabunga NEW',
            'host'      => 'mysql', // 127.0.0.1
            'port'      => '3306',
            'database'  => 'x10new',
            'username'  => 'root',
            'password'  => 'password',
        ],
        'dist_server' => [ // база куда будем сливать
            'server_name' => 'Cowabunga OLD',
            'host'      => 'mysql',
            'port'      => '3306',
            'database'  => 'x10',
            'username'  => 'root',
            'password'  => 'password',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => (bool) env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */

    'url' => env('APP_URL', 'http://localhost'),

    'asset_url' => env('ASSET_URL'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */

    'timezone' => 'Europe/Moscow',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */

    'locale' => 'cn', // Какой язык установить по умолчанию?

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
    */

    'fallback_locale' => 'cn',

    /*
    |--------------------------------------------------------------------------
    | Faker Locale
    |--------------------------------------------------------------------------
    |
    | This locale will be used by the Faker PHP library when generating fake
    | data for your database seeds. For example, this will be used to get
    | localized telephone numbers, street address information and more.
    |
    */

    'faker_locale' => 'zh_CN',

    'available_locales' => [
        'en' => 'En',
        'cn' => 'Cn',
    ],

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
    */

    'key' => env('APP_KEY'),

    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode Driver
    |--------------------------------------------------------------------------
    |
    | These configuration options determine the driver used to determine and
    | manage Laravel's "maintenance mode" status. The "cache" driver will
    | allow maintenance mode to be controlled across multiple machines.
    |
    | Supported drivers: "file", "cache"
    |
    */

    'maintenance' => [
        'driver' => 'file',
        // 'store' => 'redis',
    ],

    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
    */

    'providers' => ServiceProvider::defaultProviders()->merge([
        /*
         * Package Service Providers...
         */

        /*
         * Application Service Providers...
         */
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\HorizonServiceProvider::class,
        App\Providers\Filament\AdminPanelProvider::class,
        App\Providers\RouteServiceProvider::class,
        App\Providers\SettingServiceProvider::class,
    ])->toArray(),

    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
    */

    'aliases' => Facade::defaultAliases()->merge([
        // 'Example' => App\Facades\Example::class,
    ])->toArray(),

];
