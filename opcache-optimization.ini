; OPcache 优化配置 - 大幅提升PHP性能
zend_extension=opcache

; 启用 OPcache
opcache.enable=1
opcache.enable_cli=1

; 内存设置
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=20000

; 性能设置 - 生产环境优化
opcache.revalidate_freq=0
opcache.validate_timestamps=0
opcache.save_comments=0
opcache.fast_shutdown=1

; JIT 设置 (PHP 8.0+) - 显著提升性能
opcache.jit_buffer_size=256M
opcache.jit=tracing

; 其他优化设置
opcache.max_wasted_percentage=10
opcache.use_cwd=1
opcache.validate_permission=0
opcache.validate_root=0
opcache.file_update_protection=0

; 错误处理
opcache.log_verbosity_level=2
opcache.record_warnings=0
