<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真正的静态页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
        }
        .test-info {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .time-display {
            font-size: 1.2em;
            margin: 10px 0;
        }
        .highlight {
            color: #ffeb3b;
            font-weight: bold;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        .comparison-item {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 8px;
        }
        .fast {
            border-left: 5px solid #4caf50;
        }
        .slow {
            border-left: 5px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 真正的静态页面测试</h1>
        
        <div class="test-info">
            <h2>📊 测试结果</h2>
            <div class="time-display">页面开始加载时间: <span class="highlight" id="start-time"></span></div>
            <div class="time-display">页面加载完成时间: <span class="highlight" id="end-time"></span></div>
            <div class="time-display">总加载时间: <span class="highlight" id="total-time"></span></div>
        </div>

        <div class="test-info">
            <h2>🔍 关键发现</h2>
            <p><strong>问题所在：</strong>即使是"纯前端"页面，如果通过Laravel路由处理，仍然需要：</p>
            <ul>
                <li>✅ 启动PHP-FPM进程</li>
                <li>✅ 加载Laravel框架</li>
                <li>✅ 执行所有中间件</li>
                <li>✅ 处理路由</li>
                <li>✅ 渲染Blade模板</li>
                <li>✅ 返回HTML响应</li>
            </ul>
        </div>

        <div class="comparison">
            <div class="comparison-item slow">
                <h3>❌ Laravel路由页面</h3>
                <p><strong>URL:</strong> /speed-test</p>
                <p><strong>处理方式:</strong> PHP + Laravel + Blade</p>
                <p><strong>预期时间:</strong> 20+ 秒</p>
                <p><strong>原因:</strong> PHP-FPM性能问题</p>
            </div>
            
            <div class="comparison-item fast">
                <h3>✅ 纯静态HTML文件</h3>
                <p><strong>URL:</strong> /static-speed-test.html</p>
                <p><strong>处理方式:</strong> 直接由Nginx提供</p>
                <p><strong>预期时间:</strong> < 100ms</p>
                <p><strong>原因:</strong> 无PHP处理</p>
            </div>
        </div>

        <div class="test-info">
            <h2>💡 解决方案</h2>
            <p>如果这个静态页面加载很快，说明问题确实在PHP-FPM配置上，需要优化：</p>
            <ul>
                <li>PHP-FPM进程池配置</li>
                <li>PHP-FPM启动方式</li>
                <li>内存和进程限制</li>
                <li>可能的死锁或阻塞问题</li>
            </ul>
        </div>
    </div>

    <script>
        // 记录加载时间
        const startTime = performance.timing.navigationStart;
        const endTime = Date.now();
        const totalTime = endTime - startTime;

        document.getElementById('start-time').textContent = new Date(startTime).toLocaleTimeString();
        document.getElementById('end-time').textContent = new Date(endTime).toLocaleTimeString();
        document.getElementById('total-time').textContent = totalTime + 'ms';

        // 如果加载时间很短，说明问题确实在PHP处理上
        if (totalTime < 1000) {
            document.body.style.background = 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)';
        }
    </script>
</body>
</html>
