<?php

namespace App\Providers;

use App\Models\Referral;
use App\Models\ReferralBonus;
use App\Models\Setting;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(\App\Services\Payment\FreeKassaService::class);
        $this->app->singleton(\App\Services\Payment\MoruneService::class);
        $this->app->singleton(\App\Services\Payment\PrimePaymentsService::class);
        $this->app->singleton(\App\Services\Payment\EnotService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        View::composer('*', function ($view) {
            if (Auth::check()) {
                $userId = Auth::id();

                // 添加缓存以避免每个请求都查询数据库
                $referralsCount = Cache::remember("referrals_count_{$userId}", 300, function () use ($userId) {
                    return Referral::where('referrer_id', $userId)->count();
                });

                $totalBonus = Cache::remember("referral_bonus_{$userId}", 300, function () use ($userId) {
                    return ReferralBonus::where('referrer_id', $userId)->sum('bonus_amount');
                });

                $view->with('referralsCount', $referralsCount);
                $view->with('totalReferralBonus', $totalBonus);
            }
        });

        view()->composer('lang', function ($view) {
            $view->with('current_locale', app()->getLocale());
            $view->with('available_locales', config('app.available_locales'));
        });

        Carbon::setLocale($this->app->getLocale());

        $template = config('app.template');
        View::addNamespace('template', public_path("template/{$template}/views"));
    }

}
