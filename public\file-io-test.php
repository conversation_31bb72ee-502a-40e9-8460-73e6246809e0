<?php
// 文件I/O性能测试 - 检查Windows Docker挂载性能问题

$startTime = microtime(true);

echo "<h1>文件I/O性能测试</h1>";
echo "<p>测试开始时间: " . date('Y-m-d H:i:s') . "</p>";

// 测试1: 读取大量小文件（模拟Laravel autoload）
echo "<h2>测试1: 读取vendor目录文件</h2>";
$vendorStart = microtime(true);
$fileCount = 0;

if (is_dir('/var/www/html/vendor')) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator('/var/www/html/vendor'),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $fileCount++;
            if ($fileCount > 100) break; // 只测试前100个文件
            
            $content = file_get_contents($file->getPathname());
            // 简单处理，避免内存问题
            unset($content);
        }
    }
}

$vendorTime = microtime(true) - $vendorStart;
echo "<p>读取 {$fileCount} 个PHP文件耗时: <strong>" . round($vendorTime * 1000, 2) . "ms</strong></p>";

// 测试2: 读取Laravel核心文件
echo "<h2>测试2: 读取Laravel核心文件</h2>";
$laravelStart = microtime(true);

$laravelFiles = [
    '/var/www/html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php',
    '/var/www/html/vendor/laravel/framework/src/Illuminate/Http/Request.php',
    '/var/www/html/vendor/laravel/framework/src/Illuminate/Routing/Router.php',
    '/var/www/html/app/Http/Kernel.php',
    '/var/www/html/config/app.php'
];

$laravelFileCount = 0;
foreach ($laravelFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $laravelFileCount++;
        unset($content);
    }
}

$laravelTime = microtime(true) - $laravelStart;
echo "<p>读取 {$laravelFileCount} 个Laravel核心文件耗时: <strong>" . round($laravelTime * 1000, 2) . "ms</strong></p>";

// 测试3: 写入测试
echo "<h2>测试3: 文件写入性能</h2>";
$writeStart = microtime(true);

$testFile = '/var/www/html/storage/logs/io-test.log';
$testData = str_repeat("测试数据\n", 1000);

for ($i = 0; $i < 10; $i++) {
    file_put_contents($testFile, $testData, FILE_APPEND);
}

$writeTime = microtime(true) - $writeStart;
echo "<p>写入10次测试文件耗时: <strong>" . round($writeTime * 1000, 2) . "ms</strong></p>";

// 总结
$totalTime = microtime(true) - $startTime;
echo "<h2>总结</h2>";
echo "<p>总测试时间: <strong>" . round($totalTime * 1000, 2) . "ms</strong></p>";

if ($totalTime > 5) {
    echo "<div style='background: #ffebee; padding: 20px; border-left: 5px solid #f44336;'>";
    echo "<h3>🚨 性能问题确认</h3>";
    echo "<p>文件I/O测试耗时超过5秒，确认存在Windows Docker挂载性能问题！</p>";
    echo "<p><strong>解决方案：</strong></p>";
    echo "<ul>";
    echo "<li>使用Docker Volume而不是bind mount</li>";
    echo "<li>使用WSL2后端</li>";
    echo "<li>启用Docker Desktop的文件共享优化</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; padding: 20px; border-left: 5px solid #4caf50;'>";
    echo "<h3>✅ 文件I/O性能正常</h3>";
    echo "<p>问题可能在其他地方，需要进一步调查。</p>";
    echo "</div>";
}

// 清理测试文件
if (file_exists($testFile)) {
    unlink($testFile);
}
?>
